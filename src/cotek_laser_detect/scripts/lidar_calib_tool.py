import os
import sys
import yaml
from PyQt5.QtWidgets import (QApplication, QWidget, QLabel, QLineEdit, 
                            QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton, 
                            QRadioButton, QButtonGroup, QGroupBox, QMessageBox)
from PyQt5.QtCore import Qt

# Custom YAML representer for lists to create the desired format
def represent_list(self, data):
    return yaml.nodes.ScalarNode('tag:yaml.org,2002:str', str(data).replace("'", ""))

class LidarCalibTool(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        
    def initUI(self):
        # Main layout
        main_layout = QVBoxLayout()
        
        # Lidar number input
        lidar_layout = QHBoxLayout()
        lidar_layout.addWidget(QLabel("Lidar Number:"))
        self.lidar_number = QLineEdit()
        lidar_layout.addWidget(self.lidar_number)
        main_layout.addLayout(lidar_layout)
        
        # Offset direction selection
        offset_group = QGroupBox("Offset Direction (1.428 distance)")
        offset_layout = QHBoxLayout()
        
        self.direction_group = QButtonGroup(self)
        self.up_radio = QRadioButton("Up")
        self.down_radio = QRadioButton("Down")
        self.left_radio = QRadioButton("Left")
        self.right_radio = QRadioButton("Right")
        
        self.direction_group.addButton(self.up_radio, 1)
        self.direction_group.addButton(self.down_radio, 2)
        self.direction_group.addButton(self.left_radio, 3)
        self.direction_group.addButton(self.right_radio, 4)
        
        self.up_radio.setChecked(True)
        
        offset_layout.addWidget(self.up_radio)
        offset_layout.addWidget(self.down_radio)
        offset_layout.addWidget(self.left_radio)
        offset_layout.addWidget(self.right_radio)
        
        offset_group.setLayout(offset_layout)
        main_layout.addWidget(offset_group)

        # Quick Lidar Point Entry section
        quick_lidar_group = QGroupBox("Quick Lidar Point Entry")
        quick_lidar_layout = QVBoxLayout()

        paste_layout = QHBoxLayout()
        paste_layout.addWidget(QLabel("Paste Lidar Coordinate (x;y;z):"))
        self.pasted_lidar_coord_input = QLineEdit()
        self.pasted_lidar_coord_input.setPlaceholderText("(x.xxx;y.yyy;z.zzz)")
        paste_layout.addWidget(self.pasted_lidar_coord_input)
        quick_lidar_layout.addLayout(paste_layout)

        target_point_layout = QHBoxLayout()
        target_point_layout.addWidget(QLabel("Apply to Lidar Point:"))
        self.target_lidar_point_group = QButtonGroup(self)
        self.target_point1_radio = QRadioButton("Point 1")
        self.target_point2_radio = QRadioButton("Point 2")
        self.target_point3_radio = QRadioButton("Point 3")

        self.target_lidar_point_group.addButton(self.target_point1_radio, 0) # 0-indexed
        self.target_lidar_point_group.addButton(self.target_point2_radio, 1)
        self.target_lidar_point_group.addButton(self.target_point3_radio, 2)
        self.target_point1_radio.setChecked(True)

        target_point_layout.addWidget(self.target_point1_radio)
        target_point_layout.addWidget(self.target_point2_radio)
        target_point_layout.addWidget(self.target_point3_radio)
        target_point_layout.addStretch() 
        quick_lidar_layout.addLayout(target_point_layout)

        self.apply_pasted_btn = QPushButton("Apply Pasted Lidar Point")
        self.apply_pasted_btn.clicked.connect(self.apply_pasted_lidar_point)
        quick_lidar_layout.addWidget(self.apply_pasted_btn)
        
        quick_lidar_group.setLayout(quick_lidar_layout)
        main_layout.addWidget(quick_lidar_group)
        
        # Points input section
        points_layout = QGridLayout()
        
        # Headers
        points_layout.addWidget(QLabel("Point #"), 0, 0)
        points_layout.addWidget(QLabel("Lidar Points (x, y, z)"), 0, 1, 1, 3)
        points_layout.addWidget(QLabel("World Points (x, y, z)"), 0, 4, 1, 3) # Changed colspan
        
        # Create input fields for 3 points
        self.lidar_points = []
        self.world_points = []
        
        for i in range(3):
            # Point number
            points_layout.addWidget(QLabel(f"{i+1}"), i+1, 0)
            
            # Lidar points
            lidar_x = QLineEdit()
            lidar_y = QLineEdit()
            lidar_z = QLineEdit()
            
            points_layout.addWidget(lidar_x, i+1, 1)
            points_layout.addWidget(lidar_y, i+1, 2)
            points_layout.addWidget(lidar_z, i+1, 3)
            
            self.lidar_points.append((lidar_x, lidar_y, lidar_z))
            
            # World points
            world_x = QLineEdit("") # Default value
            world_y = QLineEdit("") # Default value
            world_z_display = QLineEdit("2.4")
            world_z_display.setReadOnly(True)
            
            points_layout.addWidget(world_x, i+1, 4)
            points_layout.addWidget(world_y, i+1, 5)
            points_layout.addWidget(world_z_display, i+1, 6) # Added Z display
            
            self.world_points.append((world_x, world_y)) # Still stores only x, y for logic
        
        main_layout.addLayout(points_layout)
        
        # Clear buttons
        clear_buttons_layout = QHBoxLayout()
        self.clear_lidar_btn = QPushButton("Clear Lidar Points")
        self.clear_lidar_btn.clicked.connect(self.clear_lidar_points)
        clear_buttons_layout.addWidget(self.clear_lidar_btn)

        self.clear_world_btn = QPushButton("Clear World Points")
        self.clear_world_btn.clicked.connect(self.clear_world_points)
        clear_buttons_layout.addWidget(self.clear_world_btn)
        main_layout.addLayout(clear_buttons_layout)

        # Generate button
        self.generate_btn = QPushButton("Generate YAML")
        self.generate_btn.clicked.connect(self.generate_yaml)
        main_layout.addWidget(self.generate_btn)
        
        self.setLayout(main_layout)
        self.setWindowTitle('Lidar Calibration Tool')
        self.resize(600, 450) # Increased height
        self.show()
    
    def clear_world_points(self):
        for wx_edit, wy_edit in self.world_points:
            wx_edit.clear()
            wy_edit.clear()

    def clear_lidar_points(self):
        for lx_edit, ly_edit, lz_edit in self.lidar_points:
            lx_edit.clear()
            ly_edit.clear()
            lz_edit.clear()

    def get_offset_direction(self):
        if self.up_radio.isChecked():
            return "up"
        elif self.down_radio.isChecked():
            return "down"
        elif self.left_radio.isChecked():
            return "left"
        elif self.right_radio.isChecked():
            return "right"
        return "up"  # Default
    
    def apply_pasted_lidar_point(self):
        pasted_text = self.pasted_lidar_coord_input.text().strip()
        target_index = self.target_lidar_point_group.checkedId()

        if target_index == -1: # Should not happen if one is checked by default
            QMessageBox.warning(self, "Selection Error", "Please select a target Lidar Point (1, 2, or 3).")
            return

        if not (pasted_text.startswith('(') and pasted_text.endswith(')')):
            QMessageBox.warning(self, "Input Error", "Pasted coordinate must be in the format (x;y;z). Missing parentheses.")
            return
        
        content_between_parentheses = pasted_text[1:-1]
        parts = content_between_parentheses.split(';')

        if len(parts) != 3:
            QMessageBox.warning(self, "Input Error", "Pasted coordinate must have three values separated by semicolons (e.g., (x.xxx;y.yyy;z.zzz)).")
            return
        
        try:
            lx_val = float(parts[0].strip())
            ly_val = float(parts[1].strip())
            lz_val = float(parts[2].strip())

            lidar_x_edit, lidar_y_edit, lidar_z_edit = self.lidar_points[target_index]
            lidar_x_edit.setText(str(lx_val))
            lidar_y_edit.setText(str(ly_val))
            lidar_z_edit.setText(str(lz_val))
            
            # Optionally clear the input field after successful application
            # self.pasted_lidar_coord_input.clear()

        except ValueError:
            QMessageBox.warning(self, "Input Error", "Invalid number format in pasted coordinate. Please use valid numbers for x, y, and z.")
            return

    def apply_offset(self, x, y, direction):
        offset = 1.428
        if direction == "up":
            return x, y + offset
        elif direction == "down":
            return x, y - offset
        elif direction == "left":
            return x - offset, y
        elif direction == "right":
            return x + offset, y
    
    def generate_yaml(self):
        try:
            # Get lidar number
            lidar_num = self.lidar_number.text().strip()
            if not lidar_num:
                QMessageBox.warning(self, "Input Error", "Please enter a lidar number")
                return
            
            # Get direction
            direction = self.get_offset_direction()
            
            # Process data for yaml output
            points_data = []
            
            # Process each point
            for i in range(3):
                try:
                    # Get lidar point
                    lx = float(self.lidar_points[i][0].text())
                    ly = float(self.lidar_points[i][1].text())
                    lz = float(self.lidar_points[i][2].text())
                    
                    # Get world point
                    wx = float(self.world_points[i][0].text())
                    wy = float(self.world_points[i][1].text())
                    
                    # Apply offset
                    wx_offset, wy_offset = self.apply_offset(wx, wy, direction)
                    
                    # Add to data
                    point_data = {
                        "lidar_point": [lx, ly, lz],
                        "world_point": [wx_offset, wy_offset, 2.4]
                    }
                    points_data.append(point_data)
                    
                except ValueError:
                    QMessageBox.warning(self, "Input Error", f"Invalid input for point {i+1}")
                    return
            
            # Create directory if it doesn't exist
            os.makedirs("yaml_calib", exist_ok=True)
            
            filename = os.path.join("yaml_calib", f"lidar{lidar_num}.yaml")

            # Check if file exists and prompt for overwrite
            if os.path.exists(filename):
                reply = QMessageBox.question(self, 'File Exists', 
                                             f"The file {filename} already exists. Do you want to overwrite it?",
                                             QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.No:
                    QMessageBox.information(self, "Cancelled", "File generation cancelled.")
                    return

            # Create the yaml structure
            data = {
                f"lidar{lidar_num}": {
                    "points": points_data
                }
            }
            
            # Write YAML file with custom formatting
            with open(filename, 'w') as f:
                # First write the header
                f.write(f"lidar{lidar_num}:\n")
                f.write("  points:\n")
                
                # Then write each point with the desired formatting
                for point in points_data:
                    f.write(f"    - lidar_point: {point['lidar_point']}\n")
                    f.write(f"      world_point: {point['world_point']}\n")
                    f.write("\n")
            
            QMessageBox.information(self, "Success", f"YAML file saved to {filename}")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = LidarCalibTool()
    sys.exit(app.exec_()) 