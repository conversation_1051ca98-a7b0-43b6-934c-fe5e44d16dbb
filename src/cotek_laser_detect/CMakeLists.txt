cmake_minimum_required(VERSION 2.8.3)
project(cotek_laser_detect)

# cmake_policy(SET CMP0046 OLD)

add_compile_options(-std=c++17 -g)

# debug编译
# set(CMAKE_BUILD_TYPE "Release")
# add_definitions(-D_GLIBCXX_USE_CXX11_ABI=1)

set(CMAKE_BUILD_TYPE Debug)

find_package(catkin REQUIRED COMPONENTS
  message_generation
  roscpp REQUIRED
    REQUIRED
  sensor_msgs 
  std_msgs
  roslib
  tf
  pcl_ros
  pcl_conversions
  pcl_msgs
)
set (CMAKE_C_FLAGS "${CMAKE_C_FLAGS}")
set (CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${TORCH_CXX_FLAGS}")
find_package(Eigen3 REQUIRED)
find_package(Boost REQUIRED COMPONENTS system)
find_package(yaml-cpp REQUIRED)

add_service_files(
  FILES
  calibrate.srv
  save_detection_cloud.srv
)

generate_messages(
  DEPENDENCIES
  std_msgs  # Or other packages containing msgs
  geometry_msgs
)

###################################
## catkin specific configuration ##
###################################
## The catkin_package macro generates cmake config files for your package
## Declare things to be passed to dependent projects
## INCLUDE_DIRS: uncomment this if your package contains header files
## LIBRARIES: libraries you create in this project that dependent projects also need
## CATKIN_DEPENDS: catkin_packages dependent projects also need
## DEPENDS: system dependencies of this project that dependent projects also need

catkin_package(
 INCLUDE_DIRS include
 LIBRARIES ${PROJECT_NAME}
 CATKIN_DEPENDS roscpp rospy sensor_msgs std_msgs roslib geometry_msgs
)

# include 

include_directories(
  include
  include/json
  ${catkin_INCLUDE_DIRS}
)

add_library(${PROJECT_NAME} 
  src/laser_detect.cc
  src/stock.cc
  src/calibration.cc
  src/ha_manager.cc
  include/json/json_reader.cpp
  include/json/json_writer.cpp
  include/json/json_value.cpp
)

add_dependencies(${PROJECT_NAME} ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_executable(${PROJECT_NAME}_node src/cotek_laser_detect_node.cc src/status_reporter.cc src/system_manager.cc)
add_dependencies(${PROJECT_NAME}_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})
add_executable(calibration_tool src/calibration_tool.cc)
target_link_libraries(${PROJECT_NAME} 
  ${catkin_LIBRARIES} 
  ${DEPENDENCIES}
  ${PCL_LIBRARIES}
  yaml-cpp
)

target_link_libraries(${PROJECT_NAME}_node 
  ${PROJECT_NAME} 
  ${catkin_LIBRARIES} 
  ${DEPENDENCIES}
  ${PCL_LIBRARIES}
)

target_link_libraries(calibration_tool 
  ${catkin_LIBRARIES} 
  ${PCL_LIBRARIES}
  ${PROJECT_NAME}
  jsoncpp
  yaml-cpp
)

#############
## Install ##
#############

## Mark executables and/or libraries for installation
install(TARGETS  ${PROJECT_NAME} ${PROJECT_NAME}_node 
  ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

## Mark cpp header files for installation
install(DIRECTORY include/
  DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}
  FILES_MATCHING PATTERN "*.h"
  PATTERN ".git" EXCLUDE
)

install(FILES
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/launch
)



