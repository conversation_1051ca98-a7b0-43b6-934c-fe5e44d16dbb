<?xml version="1.0"?>
<launch>
  <!-- <arg name="pcd_dir" default="$(find cotek_laser_detect)/config/calibrate/pcd"/> -->
  <arg name="pcd_dir" default="/home/<USER>/calibrate"/>
  <arg name="yaml_dir" default="/home/<USER>/calib_yaml/yaml_calib"/>
  <arg name="tf_json_path" default="/home/<USER>/calib_yaml/yaml_calib/tf.json"/>
  <arg name="frame_name" default=""/>
  
  <node pkg="cotek_laser_detect" type="calibration_tool" name="calibration_tool" output="screen"
        args="$(arg pcd_dir) $(arg yaml_dir) $(arg tf_json_path) $(arg frame_name)">
  </node>
</launch> 